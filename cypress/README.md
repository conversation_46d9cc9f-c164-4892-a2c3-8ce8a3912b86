# Cypress Testing for TikTok Ads Feature

Đây là bộ test Cypress cho tính năng TikTok Ads trong ứng dụng CRM360. B<PERSON> test bao gồm cả E2E testing và Component testing.

## Cấu trúc thư mục

```
cypress/
├── e2e/
│   └── tiktok-ads/
│       ├── tiktok-ads-page.cy.ts      # Test trang chính TikTok Ads
│       ├── authentication.cy.ts       # Test authentication flow
│       ├── custom-audiences.cy.ts     # Test quản lý custom audiences
│       ├── update-audience.cy.ts      # Test update audience với time validation
│       ├── account-selection.cy.ts    # Test chọn account
│       └── filter-panel.cy.ts         # Test filter panel
├── component/
│   └── tiktok-ads/
│       └── TiktokAuthContext.cy.tsx   # Test TiktokAuthContext component
├── fixtures/
│   └── tiktok-data.json              # Test data
├── support/
│   ├── commands.ts                   # Custom commands
│   ├── e2e.ts                       # E2E setup
│   ├── component.ts                 # Component setup
│   └── component-index.html         # Component test HTML
└── README.md
```

## Scripts có sẵn

```bash
# Mở Cypress Test Runner (GUI)
yarn cypress:open

# Chạy tất cả tests (headless)
yarn cypress:run

# Chạy tests trên browser cụ thể
yarn cypress:run:chrome
yarn cypress:run:firefox
yarn cypress:run:edge

# Chạy E2E tests
yarn test:e2e

# Chạy chỉ TikTok Ads E2E tests
yarn test:e2e:tiktok

# Chạy Component tests
yarn test:component

# Chạy tất cả tests
yarn test:all
```

## Các test cases chính

### 1. Authentication Tests (`authentication.cy.ts`)
- ✅ Login flow với TikTok OAuth
- ✅ Logout flow với confirmation
- ✅ Session management
- ✅ Error handling cho authentication
- ✅ Access control và authorization

### 2. TikTok Ads Page Tests (`tiktok-ads-page.cy.ts`)
- ✅ Initial page load
- ✅ Conditional rendering dựa trên authentication state
- ✅ Error handling và loading states
- ✅ Navigation và breadcrumb

### 3. Custom Audiences Tests (`custom-audiences.cy.ts`)
- ✅ Hiển thị danh sách audiences
- ✅ Filtering và search
- ✅ Pagination
- ✅ Create new audience
- ✅ Audience status display
- ✅ Error handling

### 4. Update Audience Tests (`update-audience.cy.ts`)
- ✅ Mở update modal
- ✅ Hiển thị history với infinite scroll
- ✅ **Time validation logic** - kiểm tra updated_at < 1 hour
- ✅ Segment selection
- ✅ Update process với loading states
- ✅ Error handling

### 5. Account Selection Tests (`account-selection.cy.ts`)
- ✅ Mở account selection modal
- ✅ Hiển thị available accounts
- ✅ Select và confirm account
- ✅ Refresh account list
- ✅ Error handling

### 6. Filter Panel Tests (`filter-panel.cy.ts`)
- ✅ Search filter với debouncing
- ✅ Date range filter với validation
- ✅ Multiple filters combination
- ✅ Filter state persistence
- ✅ Clear và reset filters

### 7. Component Tests (`TiktokAuthContext.cy.tsx`)
- ✅ Context initialization
- ✅ API integration
- ✅ State management
- ✅ Error handling
- ✅ Request cancellation

## Custom Commands

### E2E Commands
```typescript
cy.loginTikTok()              // Mock login
cy.mockTikTokAPI()           // Setup API mocks
cy.selectTikTokAccount(id)   // Select account
cy.waitForTikTokData()       // Wait for data load
```

### API Mocking
Tất cả API calls được mock để test độc lập:
- `GET /api/tiktok` - Get user data
- `POST /api/tiktok/ad-account` - Select account
- `GET /api/tiktok/custom-audiences` - Get audiences
- `POST /api/tiktok/large-upload` - Upload audience
- `POST /api/tiktok/large-update` - Update audience
- `POST /api/tiktok/logout` - Logout

## Test Data

File `cypress/fixtures/tiktok-data.json` chứa:
- Mock user data với multiple accounts
- Sample custom audiences với different statuses
- History data cho update modal
- Error responses cho testing

## Tính năng đặc biệt được test

### 1. Time Validation Logic
Test case quan trọng cho UpdateCustomAudience:
```typescript
// Nếu audience được update < 1 hour ago
if (timeDifferenceInHours < 1) {
  setOpenModal(false);      // Đóng modal Update
  setOpenWarning(true);     // Mở modal LimitUpdateAudience
  return;
}
// Nếu > 1 hour thì cho phép update
```

### 2. Infinite Scroll History
Test infinite scroll trong update modal:
- Load initial history items
- Scroll to bottom triggers next page
- Loading states during fetch
- Error handling

### 3. Popover Behavior
Test popover components theo user preferences:
- Chỉ trigger API khi popover mở
- Click-only interaction (không hover)
- Tránh re-rendering issues

### 4. Datatable với Pagination
Test datatable component:
- 20 items per page
- Infinite scroll optimization
- Prevent API spam

## Chạy tests

### Development
```bash
# Start dev server
yarn dev

# Mở Cypress GUI (terminal khác)
yarn cypress:open
```

### CI/CD
```bash
# Chạy tất cả tests headless
yarn test:all

# Chỉ chạy TikTok tests
yarn test:e2e:tiktok
```

## Best Practices

1. **Data-testid**: Sử dụng `data-testid` thay vì class/id
2. **API Mocking**: Mock tất cả API calls để test độc lập
3. **Error Testing**: Test cả success và error scenarios
4. **Loading States**: Test loading states và disabled buttons
5. **User Interactions**: Test real user workflows
6. **Accessibility**: Test keyboard navigation và screen readers

## Troubleshooting

### Common Issues

1. **Test timeout**: Tăng `defaultCommandTimeout` trong config
2. **API not mocked**: Kiểm tra `cy.mockTikTokAPI()` được gọi
3. **Element not found**: Kiểm tra `data-testid` attributes
4. **Flaky tests**: Thêm proper waits cho API calls

### Debug Tips

1. Sử dụng `cy.debug()` để pause test
2. Check Network tab trong Cypress để xem API calls
3. Sử dụng `cy.screenshot()` để capture state
4. Enable video recording cho failed tests

## Kết luận

Bộ test này cover toàn bộ tính năng TikTok Ads với focus đặc biệt vào:
- Time validation logic cho update audience
- Authentication flow
- Error handling
- User experience flows
- API integration

Tests được thiết kế để chạy nhanh, reliable và dễ maintain.
