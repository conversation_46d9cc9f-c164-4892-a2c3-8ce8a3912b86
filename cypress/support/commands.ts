/// <reference types="cypress" />

// Custom commands for TikTok Ads testing

Cypress.Commands.add('loginTikTok', () => {
  // Mock successful login
  cy.window().then((win) => {
    win.localStorage.setItem('tiktok_auth', 'true');
  });
});

Cypress.Commands.add('mockTikTokAPI', () => {
  // Mock TikTok user API
  cy.intercept('GET', '**/api/tiktok', {
    statusCode: 200,
    body: {
      code: 200,
      data: {
        ad_account_default: 'test_account_123',
        ad_accounts: [
          {
            ad_account_id: 'test_account_123',
            ad_account_name: 'Test TikTok Account',
            selected: true
          },
          {
            ad_account_id: 'test_account_456',
            ad_account_name: 'Test TikTok Account 2',
            selected: false
          }
        ]
      }
    }
  }).as('getTikTokUser');

  // Mock TikTok OAuth URL
  cy.intercept('GET', '**/api/tiktok/auth-link*', {
    statusCode: 200,
    body: {
      data: {
        auth_link: 'https://business-api.tiktok.com/portal/auth?app_id=test'
      }
    }
  }).as('getTikTokOAuthUrl');

  // Mock custom audiences API
  cy.intercept('GET', '**/api/tiktok/custom-audiences*', {
    statusCode: 200,
    body: {
      count: 2,
      items: [
        {
          id: 1,
          audience_name: 'Test Audience 1',
          audience_size: 1000,
          status: 'ACTIVE',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          audience_name: 'Test Audience 2',
          audience_size: 2000,
          status: 'ACTIVE',
          created_at: '2024-01-02T00:00:00Z',
          updated_at: '2024-01-02T00:00:00Z'
        }
      ]
    }
  }).as('getCustomAudiences');

  // Mock upload audience API
  cy.intercept('POST', '**/api/tiktok/large-upload', {
    statusCode: 200,
    body: {
      success: true,
      message: 'Audience uploaded successfully'
    }
  }).as('uploadAudience');

  // Mock update audience API
  cy.intercept('POST', '**/api/tiktok/large-update', {
    statusCode: 200,
    body: {
      success: true,
      message: 'Audience updated successfully'
    }
  }).as('updateAudience');

  // Mock select account API
  cy.intercept('POST', '**/api/tiktok/ad-account*', {
    statusCode: 200,
    body: {
      success: true,
      message: 'Account selected successfully'
    }
  }).as('selectAccount');

  // Mock disconnect API
  cy.intercept('POST', '**/api/tiktok/logout', {
    statusCode: 200,
    body: {
      success: true,
      message: 'Disconnected successfully'
    }
  }).as('disconnect');

  // Mock history API
  cy.intercept('GET', '**/api/custom-audience/history/*', {
    statusCode: 200,
    body: {
      count: 1,
      items: [
        {
          id: 1,
          segment_info: {
            name: 'Test Segment'
          },
          created_at: '2024-01-01T00:00:00Z'
        }
      ]
    }
  }).as('getHistory');
});

Cypress.Commands.add('selectTikTokAccount', (accountId: string) => {
  cy.get('[data-testid="account-selector"]').click();
  cy.get(`[data-testid="account-${accountId}"]`).click();
  cy.get('[data-testid="select-account-button"]').click();
  cy.wait('@selectAccount');
});

Cypress.Commands.add('waitForTikTokData', () => {
  cy.wait('@getTikTokUser');
  cy.wait('@getCustomAudiences');
});
