describe('TikTok Ads Page', () => {
  beforeEach(() => {
    cy.mockTikTokAPI();
    cy.visit('/tiktok-ads');
  });

  describe('Initial Page Load', () => {
    it('should display breadcrumb navigation', () => {
      cy.get('[data-testid="breadcrumb"]').should('be.visible');
    });

    it('should show empty state when no account is selected', () => {
      // Mock no account selected state
      cy.intercept('GET', '**/api/tiktok', {
        statusCode: 200,
        body: {
          code: 1001,
          message: 'Unauthorized'
        }
      }).as('getTikTokUserUnauth');

      cy.reload();
      cy.wait('@getTikTokUserUnauth');

      cy.get('[data-testid="empty-tiktok-view"]').should('be.visible');
      cy.get('[data-testid="header-tiktok"]').should('be.visible');
    });

    it('should show container when account is selected', () => {
      cy.loginTikTok();
      cy.reload();
      cy.waitForTikTokData();

      cy.get('[data-testid="container-tiktok-ads"]').should('be.visible');
    });
  });

  describe('Authentication Flow', () => {
    it('should handle login process', () => {
      cy.get('[data-testid="connect-tiktok-button"]').click();
      cy.wait('@getTikTokOAuthUrl');
      
      // Should redirect to TikTok OAuth (mocked)
      cy.url().should('include', 'business-api.tiktok.com');
    });

    it('should handle logout process', () => {
      cy.loginTikTok();
      cy.reload();
      cy.waitForTikTokData();

      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="logout-button"]').click();
      cy.get('[data-testid="confirm-logout"]').click();
      
      cy.wait('@disconnect');
      cy.get('[data-testid="empty-tiktok-view"]').should('be.visible');
    });
  });

  describe('Account Selection', () => {
    it('should open account selection modal', () => {
      cy.loginTikTok();
      cy.reload();
      cy.waitForTikTokData();

      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="select-account-modal"]').should('be.visible');
    });

    it('should display available accounts', () => {
      cy.loginTikTok();
      cy.reload();
      cy.waitForTikTokData();

      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="account-test_account_123"]').should('be.visible');
      cy.get('[data-testid="account-test_account_456"]').should('be.visible');
    });

    it('should select different account', () => {
      cy.loginTikTok();
      cy.reload();
      cy.waitForTikTokData();

      cy.selectTikTokAccount('test_account_456');
      
      // Should refresh data for new account
      cy.waitForTikTokData();
    });
  });

  describe('Error Handling', () => {
    it('should handle API errors gracefully', () => {
      cy.intercept('GET', '**/api/tiktok', {
        statusCode: 500,
        body: { error: 'Internal Server Error' }
      }).as('getTikTokUserError');

      cy.reload();
      cy.wait('@getTikTokUserError');

      cy.get('[data-testid="error-message"]').should('be.visible');
    });

    it('should handle network errors', () => {
      cy.intercept('GET', '**/api/tiktok', { forceNetworkError: true }).as('networkError');

      cy.reload();
      cy.wait('@networkError');

      cy.get('[data-testid="error-message"]').should('be.visible');
    });
  });

  describe('Loading States', () => {
    it('should show loading spinner during data fetch', () => {
      cy.intercept('GET', '**/api/tiktok', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({
            statusCode: 200,
            body: cy.fixture('tiktok-data').then(data => data.user)
          });
        });
      }).as('slowTikTokUser');

      cy.reload();
      cy.get('[data-testid="loading-spinner"]').should('be.visible');
      cy.wait('@slowTikTokUser');
      cy.get('[data-testid="loading-spinner"]').should('not.exist');
    });
  });
});
