describe('TikTok Ads Filter Panel', () => {
  beforeEach(() => {
    cy.mockTikTokAPI();
    cy.loginTikTok();
    cy.visit('/tiktok-ads');
    cy.waitForTikTokData();
  });

  describe('Filter Panel Display', () => {
    it('should display all filter controls', () => {
      cy.get('[data-testid="filter-panel"]').should('be.visible');
      cy.get('[data-testid="search-input"]').should('be.visible');
      cy.get('[data-testid="date-from-picker"]').should('be.visible');
      cy.get('[data-testid="date-to-picker"]').should('be.visible');
      cy.get('[data-testid="apply-filter-button"]').should('be.visible');
      cy.get('[data-testid="clear-filters-button"]').should('be.visible');
    });

    it('should show filter labels', () => {
      cy.get('[data-testid="search-label"]').should('contain', 'Search');
      cy.get('[data-testid="date-from-label"]').should('contain', 'From Date');
      cy.get('[data-testid="date-to-label"]').should('contain', 'To Date');
    });
  });

  describe('Search Filter', () => {
    it('should filter audiences by search term', () => {
      cy.get('[data-testid="search-input"]').type('Test Audience 1');
      cy.get('[data-testid="apply-filter-button"]').click();
      
      cy.wait('@getCustomAudiences');
      cy.get('[data-testid="audience-row"]').should('have.length', 1);
      cy.get('[data-testid="audience-name"]').should('contain', 'Test Audience 1');
    });

    it('should handle empty search results', () => {
      cy.intercept('GET', '**/api/tiktok/custom-audiences*', {
        statusCode: 200,
        body: { count: 0, items: [] }
      }).as('getEmptyResults');

      cy.get('[data-testid="search-input"]').type('NonExistentAudience');
      cy.get('[data-testid="apply-filter-button"]').click();
      
      cy.wait('@getEmptyResults');
      cy.get('[data-testid="no-results-message"]').should('be.visible');
    });

    it('should clear search filter', () => {
      cy.get('[data-testid="search-input"]').type('Test');
      cy.get('[data-testid="clear-search-button"]').click();
      
      cy.get('[data-testid="search-input"]').should('have.value', '');
    });

    it('should debounce search input', () => {
      cy.get('[data-testid="search-input"]').type('Test');
      
      // Should not trigger API call immediately
      cy.get('@getCustomAudiences.all').should('have.length', 1); // Only initial load
      
      // Wait for debounce and continue typing
      cy.wait(300);
      cy.get('[data-testid="search-input"]').type(' Audience');
      
      // Should trigger API call after debounce
      cy.wait(500);
      cy.get('@getCustomAudiences.all').should('have.length', 2);
    });
  });

  describe('Date Range Filter', () => {
    it('should select date range', () => {
      cy.get('[data-testid="date-from-picker"]').click();
      cy.get('[data-testid="calendar-day-1"]').click();
      
      cy.get('[data-testid="date-to-picker"]').click();
      cy.get('[data-testid="calendar-day-15"]').click();
      
      cy.get('[data-testid="apply-filter-button"]').click();
      cy.wait('@getCustomAudiences');
    });

    it('should validate date range', () => {
      // Select "to" date before "from" date
      cy.get('[data-testid="date-to-picker"]').click();
      cy.get('[data-testid="calendar-day-1"]').click();
      
      cy.get('[data-testid="date-from-picker"]').click();
      cy.get('[data-testid="calendar-day-15"]').click();
      
      cy.get('[data-testid="date-range-error"]').should('be.visible');
      cy.get('[data-testid="apply-filter-button"]').should('be.disabled');
    });

    it('should clear date filters', () => {
      cy.get('[data-testid="date-from-picker"]').click();
      cy.get('[data-testid="calendar-day-1"]').click();
      
      cy.get('[data-testid="clear-date-from"]').click();
      cy.get('[data-testid="date-from-input"]').should('have.value', '');
    });

    it('should use preset date ranges', () => {
      cy.get('[data-testid="preset-last-7-days"]').click();
      
      cy.get('[data-testid="date-from-input"]').should('not.have.value', '');
      cy.get('[data-testid="date-to-input"]').should('not.have.value', '');
      cy.get('[data-testid="apply-filter-button"]').click();
      
      cy.wait('@getCustomAudiences');
    });

    it('should handle custom date range', () => {
      cy.get('[data-testid="custom-date-range"]').click();
      
      cy.get('[data-testid="date-from-input"]').type('2024-01-01');
      cy.get('[data-testid="date-to-input"]').type('2024-01-31');
      
      cy.get('[data-testid="apply-filter-button"]').click();
      cy.wait('@getCustomAudiences');
    });
  });

  describe('Filter Combinations', () => {
    it('should apply multiple filters together', () => {
      cy.get('[data-testid="search-input"]').type('Test');
      
      cy.get('[data-testid="date-from-picker"]').click();
      cy.get('[data-testid="calendar-day-1"]').click();
      
      cy.get('[data-testid="date-to-picker"]').click();
      cy.get('[data-testid="calendar-day-15"]').click();
      
      cy.get('[data-testid="apply-filter-button"]').click();
      
      cy.wait('@getCustomAudiences').then((interception) => {
        expect(interception.request.query).to.include({
          search: 'Test',
          date_created_from: '2024-01-01',
          date_created_to: '2024-01-15'
        });
      });
    });

    it('should clear all filters at once', () => {
      cy.get('[data-testid="search-input"]').type('Test');
      cy.get('[data-testid="date-from-picker"]').click();
      cy.get('[data-testid="calendar-day-1"]').click();
      
      cy.get('[data-testid="clear-filters-button"]').click();
      
      cy.get('[data-testid="search-input"]').should('have.value', '');
      cy.get('[data-testid="date-from-input"]').should('have.value', '');
      cy.get('[data-testid="date-to-input"]').should('have.value', '');
      
      cy.wait('@getCustomAudiences');
    });
  });

  describe('Filter State Persistence', () => {
    it('should persist filters in URL', () => {
      cy.get('[data-testid="search-input"]').type('Test');
      cy.get('[data-testid="apply-filter-button"]').click();
      
      cy.url().should('include', 'search=Test');
    });

    it('should restore filters from URL on page load', () => {
      cy.visit('/tiktok-ads?search=Test&date_from=2024-01-01&date_to=2024-01-15');
      cy.waitForTikTokData();
      
      cy.get('[data-testid="search-input"]').should('have.value', 'Test');
      cy.get('[data-testid="date-from-input"]').should('have.value', '2024-01-01');
      cy.get('[data-testid="date-to-input"]').should('have.value', '2024-01-15');
    });

    it('should maintain filters during pagination', () => {
      cy.get('[data-testid="search-input"]').type('Test');
      cy.get('[data-testid="apply-filter-button"]').click();
      
      cy.get('[data-testid="pagination-next"]').click();
      
      cy.get('[data-testid="search-input"]').should('have.value', 'Test');
      cy.wait('@getCustomAudiences').then((interception) => {
        expect(interception.request.query).to.include({
          search: 'Test',
          page: '2'
        });
      });
    });
  });

  describe('Filter Performance', () => {
    it('should show loading state during filter application', () => {
      cy.intercept('GET', '**/api/tiktok/custom-audiences*', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({
            statusCode: 200,
            body: cy.fixture('tiktok-data').then(data => data.customAudiences)
          });
        });
      }).as('slowFilter');

      cy.get('[data-testid="search-input"]').type('Test');
      cy.get('[data-testid="apply-filter-button"]').click();
      
      cy.get('[data-testid="filter-loading"]').should('be.visible');
      cy.get('[data-testid="apply-filter-button"]').should('be.disabled');
      
      cy.wait('@slowFilter');
      cy.get('[data-testid="filter-loading"]').should('not.exist');
    });

    it('should cancel previous filter request when new filter is applied', () => {
      cy.get('[data-testid="search-input"]').type('Test1');
      cy.get('[data-testid="apply-filter-button"]').click();
      
      // Immediately apply another filter
      cy.get('[data-testid="search-input"]').clear().type('Test2');
      cy.get('[data-testid="apply-filter-button"]').click();
      
      // Should only have the latest request
      cy.wait('@getCustomAudiences');
      cy.get('@getCustomAudiences.all').should('have.length', 2); // Initial + latest
    });
  });

  describe('Filter Validation', () => {
    it('should validate required fields', () => {
      cy.get('[data-testid="date-from-picker"]').click();
      cy.get('[data-testid="calendar-day-1"]').click();
      
      // Missing "to" date should show validation
      cy.get('[data-testid="apply-filter-button"]').click();
      cy.get('[data-testid="date-to-required"]').should('be.visible');
    });

    it('should validate date format', () => {
      cy.get('[data-testid="date-from-input"]').type('invalid-date');
      cy.get('[data-testid="date-format-error"]').should('be.visible');
      cy.get('[data-testid="apply-filter-button"]').should('be.disabled');
    });

    it('should validate search length', () => {
      cy.get('[data-testid="search-input"]').type('a'); // Too short
      cy.get('[data-testid="search-min-length"]').should('be.visible');
      cy.get('[data-testid="apply-filter-button"]').should('be.disabled');
    });
  });

  describe('Filter Reset', () => {
    it('should reset to default state', () => {
      cy.get('[data-testid="search-input"]').type('Test');
      cy.get('[data-testid="date-from-picker"]').click();
      cy.get('[data-testid="calendar-day-1"]').click();
      
      cy.get('[data-testid="reset-filters-button"]').click();
      
      cy.get('[data-testid="search-input"]').should('have.value', '');
      cy.get('[data-testid="date-from-input"]').should('have.value', '');
      cy.get('[data-testid="date-to-input"]').should('have.value', '');
      
      cy.wait('@getCustomAudiences').then((interception) => {
        expect(interception.request.query).to.not.have.property('search');
        expect(interception.request.query).to.not.have.property('date_created_from');
      });
    });

    it('should show confirmation before reset', () => {
      cy.get('[data-testid="search-input"]').type('Test');
      cy.get('[data-testid="reset-filters-button"]').click();
      
      cy.get('[data-testid="reset-confirmation"]').should('be.visible');
      cy.get('[data-testid="confirm-reset"]').click();
      
      cy.get('[data-testid="search-input"]').should('have.value', '');
    });
  });
});
