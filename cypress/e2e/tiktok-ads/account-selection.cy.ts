describe('TikTok Account Selection', () => {
  beforeEach(() => {
    cy.mockTikTokAPI();
    cy.loginTikTok();
    cy.visit('/tiktok-ads');
    cy.waitForTikTokData();
  });

  describe('Account Selection Modal', () => {
    it('should open account selection modal from header dropdown', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="select-account-modal"]').should('be.visible');
      cy.get('[data-testid="modal-title"]').should('contain', 'Select Account');
    });

    it('should display available accounts', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="account-list"]').should('be.visible');
      cy.get('[data-testid="account-item"]').should('have.length', 2);
      
      cy.get('[data-testid="account-test_account_123"]').should('be.visible');
      cy.get('[data-testid="account-test_account_456"]').should('be.visible');
    });

    it('should show account details', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="account-test_account_123"]').within(() => {
        cy.get('[data-testid="account-name"]').should('contain', 'Test TikTok Account');
        cy.get('[data-testid="account-id"]').should('contain', 'test_account_123');
        cy.get('[data-testid="account-radio"]').should('be.checked');
      });
    });

    it('should highlight currently selected account', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="account-test_account_123"]').should('have.class', 'selected');
      cy.get('[data-testid="account-test_account_456"]').should('not.have.class', 'selected');
    });
  });

  describe('Account Selection Process', () => {
    it('should select different account', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="account-test_account_456"]').click();
      cy.get('[data-testid="account-test_account_456"]').within(() => {
        cy.get('[data-testid="account-radio"]').should('be.checked');
      });
    });

    it('should confirm account selection', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="account-test_account_456"]').click();
      cy.get('[data-testid="confirm-selection-button"]').click();
      
      cy.wait('@selectAccount');
      cy.get('[data-testid="success-toast"]').should('be.visible');
      cy.get('[data-testid="select-account-modal"]').should('not.exist');
    });

    it('should update header with new account name', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="account-test_account_456"]').click();
      cy.get('[data-testid="confirm-selection-button"]').click();
      
      cy.wait('@selectAccount');
      cy.get('[data-testid="current-account-name"]').should('contain', 'Test TikTok Account 2');
    });

    it('should refresh audience data after account change', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="account-test_account_456"]').click();
      cy.get('[data-testid="confirm-selection-button"]').click();
      
      cy.wait('@selectAccount');
      cy.wait('@getCustomAudiences');
    });
  });

  describe('Modal Actions', () => {
    it('should close modal when clicking cancel', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="cancel-selection-button"]').click();
      cy.get('[data-testid="select-account-modal"]').should('not.exist');
    });

    it('should close modal when clicking outside', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="modal-overlay"]').click({ force: true });
      cy.get('[data-testid="select-account-modal"]').should('not.exist');
    });

    it('should close modal with escape key', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('body').type('{esc}');
      cy.get('[data-testid="select-account-modal"]').should('not.exist');
    });
  });

  describe('Loading States', () => {
    it('should show loading state during account selection', () => {
      cy.intercept('POST', '**/api/tiktok/ad-account*', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({ statusCode: 200, body: { success: true } });
        });
      }).as('slowSelectAccount');

      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="account-test_account_456"]').click();
      cy.get('[data-testid="confirm-selection-button"]').click();
      
      cy.get('[data-testid="confirm-selection-button"]').should('be.disabled');
      cy.get('[data-testid="loading-spinner"]').should('be.visible');
      
      cy.wait('@slowSelectAccount');
      cy.get('[data-testid="success-toast"]').should('be.visible');
    });

    it('should disable confirm button when no account is selected', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      // Uncheck current selection
      cy.get('[data-testid="account-test_account_123"]').click();
      cy.get('[data-testid="confirm-selection-button"]').should('be.disabled');
    });
  });

  describe('No Accounts Available', () => {
    it('should handle empty account list', () => {
      cy.intercept('GET', '**/api/tiktok', {
        statusCode: 200,
        body: {
          code: 200,
          data: {
            ad_account_default: null,
            ad_accounts: []
          }
        }
      }).as('getEmptyAccounts');

      cy.reload();
      cy.wait('@getEmptyAccounts');

      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="no-accounts-message"]').should('be.visible');
      cy.get('[data-testid="confirm-selection-button"]').should('be.disabled');
    });

    it('should show message when no accounts are available', () => {
      cy.intercept('GET', '**/api/tiktok', {
        statusCode: 200,
        body: {
          code: 200,
          data: {
            ad_account_default: null,
            ad_accounts: []
          }
        }
      }).as('getEmptyAccounts');

      cy.reload();
      cy.wait('@getEmptyAccounts');

      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="no-accounts-message"]')
        .should('contain', 'No TikTok accounts available');
    });
  });

  describe('Error Handling', () => {
    it('should handle account selection API error', () => {
      cy.intercept('POST', '**/api/tiktok/ad-account*', {
        statusCode: 400,
        body: { error: 'Failed to select account' }
      }).as('selectAccountError');

      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="account-test_account_456"]').click();
      cy.get('[data-testid="confirm-selection-button"]').click();
      
      cy.wait('@selectAccountError');
      cy.get('[data-testid="error-toast"]').should('be.visible');
      cy.get('[data-testid="select-account-modal"]').should('be.visible');
    });

    it('should handle network error during account selection', () => {
      cy.intercept('POST', '**/api/tiktok/ad-account*', { forceNetworkError: true }).as('networkError');

      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="account-test_account_456"]').click();
      cy.get('[data-testid="confirm-selection-button"]').click();
      
      cy.wait('@networkError');
      cy.get('[data-testid="error-toast"]').should('be.visible');
    });
  });

  describe('Refresh Accounts', () => {
    it('should refresh account list', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="refresh-accounts-button"]').click();
      cy.wait('@getTikTokUser');
      
      cy.get('[data-testid="success-toast"]').should('contain', 'Accounts refreshed');
    });

    it('should show loading state during refresh', () => {
      cy.intercept('GET', '**/api/tiktok', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({
            statusCode: 200,
            body: cy.fixture('tiktok-data').then(data => data.user)
          });
        });
      }).as('slowRefresh');

      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="select-account-button"]').click();
      
      cy.get('[data-testid="refresh-accounts-button"]').click();
      cy.get('[data-testid="refresh-loading"]').should('be.visible');
      
      cy.wait('@slowRefresh');
      cy.get('[data-testid="refresh-loading"]').should('not.exist');
    });
  });
});
