describe('TikTok Authentication', () => {
  beforeEach(() => {
    cy.mockTikTokAPI();
    cy.visit('/tiktok-ads');
  });

  describe('Login Flow', () => {
    it('should display connect button when not authenticated', () => {
      cy.get('[data-testid="connect-tiktok-button"]').should('be.visible');
      cy.get('[data-testid="connect-tiktok-button"]').should('contain', 'Connect TikTok');
    });

    it('should redirect to TikTok OAuth when clicking connect', () => {
      cy.get('[data-testid="connect-tiktok-button"]').click();
      cy.wait('@getTikTokOAuthUrl');
      
      // In real scenario, this would redirect to TikTok
      // For testing, we mock the redirect
      cy.url().should('include', 'business-api.tiktok.com');
    });

    it('should handle OAuth callback success', () => {
      // Simulate successful OAuth callback
      cy.visit('/tiktok-ads?code=success_code&state=test_state');
      cy.waitForTikTokData();
      
      cy.get('[data-testid="container-tiktok-ads"]').should('be.visible');
      cy.get('[data-testid="connect-tiktok-button"]').should('not.exist');
    });

    it('should handle OAuth callback error', () => {
      cy.visit('/tiktok-ads?error=access_denied&error_description=User%20denied%20access');
      
      cy.get('[data-testid="oauth-error-message"]').should('be.visible');
      cy.get('[data-testid="oauth-error-message"]').should('contain', 'Access denied');
    });
  });

  describe('Authentication State', () => {
    it('should persist authentication state', () => {
      cy.loginTikTok();
      cy.reload();
      cy.waitForTikTokData();
      
      cy.get('[data-testid="container-tiktok-ads"]').should('be.visible');
    });

    it('should handle expired authentication', () => {
      cy.intercept('GET', '**/api/tiktok', {
        statusCode: 401,
        body: { code: 1001, message: 'Unauthorized' }
      }).as('getUnauthorized');

      cy.loginTikTok();
      cy.reload();
      cy.wait('@getUnauthorized');
      
      cy.get('[data-testid="empty-tiktok-view"]').should('be.visible');
      cy.get('[data-testid="connect-tiktok-button"]').should('be.visible');
    });

    it('should clear authentication on logout', () => {
      cy.loginTikTok();
      cy.reload();
      cy.waitForTikTokData();
      
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="logout-button"]').click();
      cy.get('[data-testid="confirm-logout"]').click();
      
      cy.wait('@disconnect');
      cy.get('[data-testid="empty-tiktok-view"]').should('be.visible');
    });
  });

  describe('Logout Flow', () => {
    beforeEach(() => {
      cy.loginTikTok();
      cy.reload();
      cy.waitForTikTokData();
    });

    it('should show logout confirmation modal', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="logout-button"]').click();
      
      cy.get('[data-testid="logout-confirmation-modal"]').should('be.visible');
      cy.get('[data-testid="logout-warning-message"]').should('be.visible');
    });

    it('should cancel logout process', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="logout-button"]').click();
      
      cy.get('[data-testid="cancel-logout"]').click();
      cy.get('[data-testid="logout-confirmation-modal"]').should('not.exist');
      cy.get('[data-testid="container-tiktok-ads"]').should('be.visible');
    });

    it('should complete logout process', () => {
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="logout-button"]').click();
      cy.get('[data-testid="confirm-logout"]').click();
      
      cy.wait('@disconnect');
      cy.get('[data-testid="success-toast"]').should('contain', 'Disconnected successfully');
      cy.get('[data-testid="empty-tiktok-view"]').should('be.visible');
    });

    it('should handle logout API error', () => {
      cy.intercept('POST', '**/api/tiktok/logout', {
        statusCode: 500,
        body: { error: 'Logout failed' }
      }).as('logoutError');

      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="logout-button"]').click();
      cy.get('[data-testid="confirm-logout"]').click();
      
      cy.wait('@logoutError');
      cy.get('[data-testid="error-toast"]').should('be.visible');
    });
  });

  describe('Access Control', () => {
    it('should show access denied page for unauthorized users', () => {
      cy.intercept('GET', '**/api/tiktok', {
        statusCode: 403,
        body: { code: 403, message: 'Access denied' }
      }).as('getAccessDenied');

      cy.reload();
      cy.wait('@getAccessDenied');
      
      cy.get('[data-testid="access-denied-page"]').should('be.visible');
      cy.get('[data-testid="access-denied-message"]').should('contain', 'Access denied');
    });

    it('should show authorization failed page for failed auth', () => {
      cy.visit('/tiktok-ads/authorize-failed');
      
      cy.get('[data-testid="authorize-failed-page"]').should('be.visible');
      cy.get('[data-testid="retry-auth-button"]').should('be.visible');
    });

    it('should retry authorization from failed page', () => {
      cy.visit('/tiktok-ads/authorize-failed');
      
      cy.get('[data-testid="retry-auth-button"]').click();
      cy.wait('@getTikTokOAuthUrl');
    });
  });

  describe('Loading States', () => {
    it('should show loading spinner during authentication check', () => {
      cy.intercept('GET', '**/api/tiktok', (req) => {
        req.reply((res) => {
          res.delay(1000);
          res.send({
            statusCode: 200,
            body: cy.fixture('tiktok-data').then(data => data.user)
          });
        });
      }).as('slowAuth');

      cy.reload();
      cy.get('[data-testid="auth-loading"]').should('be.visible');
      cy.wait('@slowAuth');
      cy.get('[data-testid="auth-loading"]').should('not.exist');
    });

    it('should show loading state during OAuth redirect', () => {
      cy.intercept('GET', '**/api/tiktok/auth-link*', (req) => {
        req.reply((res) => {
          res.delay(500);
          res.send({
            statusCode: 200,
            body: { data: { auth_link: 'https://business-api.tiktok.com/portal/auth' } }
          });
        });
      }).as('slowOAuth');

      cy.get('[data-testid="connect-tiktok-button"]').click();
      cy.get('[data-testid="oauth-loading"]').should('be.visible');
      cy.wait('@slowOAuth');
    });
  });

  describe('Error Handling', () => {
    it('should handle authentication API timeout', () => {
      cy.intercept('GET', '**/api/tiktok', { forceNetworkError: true }).as('authTimeout');

      cy.reload();
      cy.wait('@authTimeout');
      
      cy.get('[data-testid="auth-error"]').should('be.visible');
      cy.get('[data-testid="retry-auth-button"]').should('be.visible');
    });

    it('should retry authentication after error', () => {
      cy.intercept('GET', '**/api/tiktok', { forceNetworkError: true }).as('authError');

      cy.reload();
      cy.wait('@authError');
      
      // Mock successful retry
      cy.intercept('GET', '**/api/tiktok', {
        statusCode: 200,
        body: cy.fixture('tiktok-data').then(data => data.user)
      }).as('authRetrySuccess');

      cy.get('[data-testid="retry-auth-button"]').click();
      cy.wait('@authRetrySuccess');
      cy.get('[data-testid="container-tiktok-ads"]').should('be.visible');
    });

    it('should handle OAuth URL generation error', () => {
      cy.intercept('GET', '**/api/tiktok/auth-link*', {
        statusCode: 500,
        body: { error: 'Failed to generate OAuth URL' }
      }).as('oauthError');

      cy.get('[data-testid="connect-tiktok-button"]').click();
      cy.wait('@oauthError');
      
      cy.get('[data-testid="oauth-error"]').should('be.visible');
    });
  });

  describe('Session Management', () => {
    it('should handle concurrent sessions', () => {
      cy.loginTikTok();
      
      // Simulate session conflict
      cy.intercept('GET', '**/api/tiktok', {
        statusCode: 409,
        body: { code: 409, message: 'Session conflict' }
      }).as('sessionConflict');

      cy.reload();
      cy.wait('@sessionConflict');
      
      cy.get('[data-testid="session-conflict-modal"]').should('be.visible');
    });

    it('should refresh session when needed', () => {
      cy.loginTikTok();
      cy.reload();
      cy.waitForTikTokData();
      
      // Simulate session refresh
      cy.get('[data-testid="header-dropdown"]').click();
      cy.get('[data-testid="refresh-session-button"]').click();
      
      cy.wait('@getTikTokUser');
      cy.get('[data-testid="success-toast"]').should('contain', 'Session refreshed');
    });
  });
});
