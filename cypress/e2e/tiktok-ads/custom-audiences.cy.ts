describe('TikTok Custom Audiences', () => {
  beforeEach(() => {
    cy.mockTikTokAPI();
    cy.loginTikTok();
    cy.visit('/tiktok-ads');
    cy.waitForTikTokData();
  });

  describe('Audience List Display', () => {
    it('should display custom audiences table', () => {
      cy.get('[data-testid="audiences-table"]').should('be.visible');
      cy.get('[data-testid="audience-row"]').should('have.length.at.least', 1);
    });

    it('should show audience details in table', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="audience-name"]').should('contain', 'Test Audience');
        cy.get('[data-testid="audience-size"]').should('be.visible');
        cy.get('[data-testid="audience-status"]').should('be.visible');
        cy.get('[data-testid="audience-created-date"]').should('be.visible');
      });
    });

    it('should handle empty audience list', () => {
      cy.intercept('GET', '**/api/tiktok/custom-audiences*', {
        statusCode: 200,
        body: { count: 0, items: [] }
      }).as('getEmptyAudiences');

      cy.reload();
      cy.wait('@getEmptyAudiences');

      cy.get('[data-testid="no-data-message"]').should('be.visible');
    });
  });

  describe('Audience Filtering', () => {
    it('should filter audiences by search term', () => {
      cy.get('[data-testid="search-input"]').type('Test Audience 1');
      cy.get('[data-testid="audience-row"]').should('have.length', 1);
      cy.get('[data-testid="audience-name"]').should('contain', 'Test Audience 1');
    });

    it('should filter by date range', () => {
      cy.get('[data-testid="date-from-picker"]').click();
      cy.get('[data-testid="date-2024-01-01"]').click();
      
      cy.get('[data-testid="date-to-picker"]').click();
      cy.get('[data-testid="date-2024-01-02"]').click();

      cy.get('[data-testid="apply-filter-button"]').click();
      
      // Should make API call with date filters
      cy.wait('@getCustomAudiences');
    });

    it('should clear filters', () => {
      cy.get('[data-testid="search-input"]').type('Test');
      cy.get('[data-testid="clear-filters-button"]').click();
      
      cy.get('[data-testid="search-input"]').should('have.value', '');
      cy.wait('@getCustomAudiences');
    });
  });

  describe('Pagination', () => {
    it('should handle pagination', () => {
      // Mock paginated response
      cy.intercept('GET', '**/api/tiktok/custom-audiences*', (req) => {
        const page = req.query.page || 1;
        req.reply({
          statusCode: 200,
          body: {
            count: 25,
            items: Array.from({ length: 10 }, (_, i) => ({
              id: i + 1 + (page - 1) * 10,
              audience_name: `Audience ${i + 1 + (page - 1) * 10}`,
              audience_size: 1000,
              status: 'ACTIVE',
              created_at: '2024-01-01T00:00:00Z',
              updated_at: '2024-01-01T00:00:00Z'
            }))
          }
        });
      }).as('getPaginatedAudiences');

      cy.reload();
      cy.wait('@getPaginatedAudiences');

      cy.get('[data-testid="pagination-next"]').click();
      cy.wait('@getPaginatedAudiences');
      
      cy.get('[data-testid="audience-name"]').first().should('contain', 'Audience 11');
    });

    it('should change page size', () => {
      cy.get('[data-testid="page-size-selector"]').click();
      cy.get('[data-testid="page-size-20"]').click();
      
      cy.wait('@getCustomAudiences');
    });
  });

  describe('Audience Actions', () => {
    it('should open add audience modal', () => {
      cy.get('[data-testid="add-audience-button"]').click();
      cy.get('[data-testid="add-audience-modal"]').should('be.visible');
    });

    it('should create new audience', () => {
      cy.get('[data-testid="add-audience-button"]').click();
      
      cy.get('[data-testid="audience-name-input"]').type('New Test Audience');
      cy.get('[data-testid="segment-selector"]').click();
      cy.get('[data-testid="segment-option-1"]').click();
      
      cy.get('[data-testid="create-audience-button"]').click();
      
      cy.wait('@uploadAudience');
      cy.get('[data-testid="success-toast"]').should('be.visible');
    });

    it('should open update audience modal', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="update-audience-modal"]').should('be.visible');
    });

    it('should show history in update modal', () => {
      cy.get('[data-testid="audience-row"]').first().within(() => {
        cy.get('[data-testid="update-button"]').click();
      });
      
      cy.get('[data-testid="history-section"]').should('be.visible');
      cy.get('[data-testid="history-item"]').should('have.length.at.least', 1);
    });
  });

  describe('Audience Status', () => {
    it('should display different status badges', () => {
      cy.get('[data-testid="audience-row"]').each(($row) => {
        cy.wrap($row).within(() => {
          cy.get('[data-testid="status-badge"]').should('be.visible');
        });
      });
    });

    it('should handle processing status', () => {
      cy.get('[data-testid="audience-row"]').contains('PROCESSING').within(() => {
        cy.get('[data-testid="status-badge"]').should('have.class', 'processing');
      });
    });

    it('should handle active status', () => {
      cy.get('[data-testid="audience-row"]').contains('ACTIVE').within(() => {
        cy.get('[data-testid="status-badge"]').should('have.class', 'active');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle audience creation error', () => {
      cy.intercept('POST', '**/api/tiktok/large-upload', {
        statusCode: 400,
        body: { error: 'Invalid audience data' }
      }).as('uploadAudienceError');

      cy.get('[data-testid="add-audience-button"]').click();
      cy.get('[data-testid="audience-name-input"]').type('New Test Audience');
      cy.get('[data-testid="segment-selector"]').click();
      cy.get('[data-testid="segment-option-1"]').click();
      cy.get('[data-testid="create-audience-button"]').click();
      
      cy.wait('@uploadAudienceError');
      cy.get('[data-testid="error-toast"]').should('be.visible');
    });

    it('should handle audience loading error', () => {
      cy.intercept('GET', '**/api/tiktok/custom-audiences*', {
        statusCode: 500,
        body: { error: 'Server error' }
      }).as('getAudiencesError');

      cy.reload();
      cy.wait('@getAudiencesError');

      cy.get('[data-testid="error-message"]').should('be.visible');
    });
  });
});
