describe('Simple Application Test', () => {
  it('should load the application homepage', () => {
    cy.visit('/')
    cy.get('body').should('be.visible')
  })

  it('should navigate to TikTok Ads page', () => {
    cy.visit('/tiktok-ads')
    cy.get('body').should('be.visible')
    
    // Check if page loads without errors
    cy.get('html').should('exist')
  })

  it('should display TikTok page elements', () => {
    // Mock the TikTok API to avoid real API calls
    cy.intercept('GET', '**/api/v1/tiktok-ads/**', {
      statusCode: 200,
      body: {
        code: 1001,
        message: 'Unauthorized'
      }
    }).as('getTikTokUser')

    cy.visit('/tiktok-ads')
    
    // Wait a bit for the page to load
    cy.wait(2000)
    
    // Check if basic elements exist - should contain at least one of these texts
    cy.get('body').should('be.visible')
    cy.get('body').then(($body) => {
      const text = $body.text()
      expect(text).to.satisfy((str: string) => {
        return str.includes('TikTok') || str.includes('Connect') || str.includes('Ads')
      })
    })
  })

  it('should handle TikTok authentication state', () => {
    // Mock unauthorized response
    cy.intercept('GET', '**/api/v1/tiktok-ads/**', {
      statusCode: 200,
      body: {
        code: 1001,
        message: 'Unauthorized'
      }
    }).as('getTikTokUserUnauth')

    cy.visit('/tiktok-ads')
    cy.wait(3000)
    
    // Should show some content even if not authenticated
    cy.get('body').should('be.visible')
  })

  it('should mock successful TikTok authentication', () => {
    // Mock successful response
    cy.intercept('GET', '**/api/v1/tiktok-ads/**', {
      statusCode: 200,
      body: {
        code: 200,
        data: {
          ad_account_default: 'test_account_123',
          ad_accounts: [
            {
              ad_account_id: 'test_account_123',
              ad_account_name: 'Test TikTok Account',
              selected: true
            }
          ]
        }
      }
    }).as('getTikTokUserSuccess')

    cy.visit('/tiktok-ads')
    cy.wait(3000)
    
    // Should show authenticated content
    cy.get('body').should('be.visible')
  })
})
