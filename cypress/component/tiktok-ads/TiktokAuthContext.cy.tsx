import React from 'react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { TiktokAuthProvider, useTiktokContext } from '../../../src/pages/TiktokAds/context/TiktokAuthContext'

// Test component to access context
const TestComponent = () => {
  const {
    listPages,
    adsAccount,
    isLogin,
    loading,
    refetchLoading,
    isAccountSelected,
    updateStateSelected,
    logout,
    handleRefetchListPage
  } = useTiktokContext()

  return (
    <div>
      <div data-testid="is-login">{isLogin.toString()}</div>
      <div data-testid="loading">{loading.toString()}</div>
      <div data-testid="refetch-loading">{refetchLoading.toString()}</div>
      <div data-testid="is-account-selected">{isAccountSelected.toString()}</div>
      <div data-testid="list-pages-count">{listPages.length}</div>
      <div data-testid="ads-account-id">{adsAccount?.ad_account_id || 'none'}</div>
      <div data-testid="ads-account-name">{adsAccount?.ad_account_name || 'none'}</div>
      
      <button data-testid="update-state-btn" onClick={() => updateStateSelected('test_account_456')}>
        Update State
      </button>
      <button data-testid="logout-btn" onClick={logout}>
        Logout
      </button>
      <button data-testid="refetch-btn" onClick={handleRefetchListPage}>
        Refetch
      </button>
    </div>
  )
}

const Wrapper = ({ children }: { children: React.ReactNode }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      <TiktokAuthProvider>
        {children}
      </TiktokAuthProvider>
    </QueryClientProvider>
  )
}

describe('TiktokAuthContext', () => {
  beforeEach(() => {
    // Mock API responses
    cy.intercept('GET', '**/api/tiktok', {
      statusCode: 200,
      body: {
        code: 200,
        data: {
          ad_account_default: 'test_account_123',
          ad_accounts: [
            {
              ad_account_id: 'test_account_123',
              ad_account_name: 'Test TikTok Account',
              selected: true
            },
            {
              ad_account_id: 'test_account_456',
              ad_account_name: 'Test TikTok Account 2',
              selected: false
            }
          ]
        }
      }
    }).as('getTikTokUser')

    cy.intercept('POST', '**/api/tiktok/logout', {
      statusCode: 200,
      body: { success: true }
    }).as('logout')
  })

  it('should initialize with default state', () => {
    cy.mount(
      <Wrapper>
        <TestComponent />
      </Wrapper>
    )

    cy.get('[data-testid="loading"]').should('contain', 'true')
    cy.get('[data-testid="is-login"]').should('contain', 'false')
    cy.get('[data-testid="is-account-selected"]').should('contain', 'false')
    cy.get('[data-testid="list-pages-count"]').should('contain', '0')
  })

  it('should load user data on mount', () => {
    cy.mount(
      <Wrapper>
        <TestComponent />
      </Wrapper>
    )

    cy.wait('@getTikTokUser')
    
    cy.get('[data-testid="loading"]').should('contain', 'false')
    cy.get('[data-testid="is-login"]').should('contain', 'true')
    cy.get('[data-testid="is-account-selected"]').should('contain', 'true')
    cy.get('[data-testid="list-pages-count"]').should('contain', '2')
    cy.get('[data-testid="ads-account-id"]').should('contain', 'test_account_123')
    cy.get('[data-testid="ads-account-name"]').should('contain', 'Test TikTok Account')
  })

  it('should handle unauthorized response', () => {
    cy.intercept('GET', '**/api/tiktok', {
      statusCode: 200,
      body: {
        code: 1001,
        message: 'Unauthorized'
      }
    }).as('getTikTokUserUnauth')

    cy.mount(
      <Wrapper>
        <TestComponent />
      </Wrapper>
    )

    cy.wait('@getTikTokUserUnauth')
    
    cy.get('[data-testid="loading"]').should('contain', 'false')
    cy.get('[data-testid="is-login"]').should('contain', 'false')
    cy.get('[data-testid="is-account-selected"]').should('contain', 'false')
    cy.get('[data-testid="list-pages-count"]').should('contain', '0')
  })

  it('should update selected state', () => {
    cy.mount(
      <Wrapper>
        <TestComponent />
      </Wrapper>
    )

    cy.wait('@getTikTokUser')
    
    cy.get('[data-testid="update-state-btn"]').click()
    
    // Should update localStorage and state
    cy.window().its('localStorage').invoke('getItem', 'tiktok_auth').should('eq', 'true')
  })

  it('should handle logout', () => {
    cy.mount(
      <Wrapper>
        <TestComponent />
      </Wrapper>
    )

    cy.wait('@getTikTokUser')
    
    cy.get('[data-testid="logout-btn"]').click()
    cy.wait('@logout')
    
    cy.get('[data-testid="is-login"]').should('contain', 'false')
    cy.get('[data-testid="list-pages-count"]').should('contain', '0')
    cy.get('[data-testid="ads-account-id"]').should('contain', 'none')
  })

  it('should handle refetch', () => {
    cy.mount(
      <Wrapper>
        <TestComponent />
      </Wrapper>
    )

    cy.wait('@getTikTokUser')
    
    cy.get('[data-testid="refetch-btn"]').click()
    
    cy.get('[data-testid="refetch-loading"]').should('contain', 'true')
    cy.wait('@getTikTokUser')
    cy.get('[data-testid="refetch-loading"]').should('contain', 'false')
  })

  it('should handle API errors', () => {
    cy.intercept('GET', '**/api/tiktok', {
      statusCode: 500,
      body: { error: 'Server error' }
    }).as('getTikTokUserError')

    cy.mount(
      <Wrapper>
        <TestComponent />
      </Wrapper>
    )

    cy.wait('@getTikTokUserError')
    
    cy.get('[data-testid="loading"]').should('contain', 'false')
    cy.get('[data-testid="is-login"]').should('contain', 'false')
  })

  it('should abort previous requests on new requests', () => {
    let requestCount = 0
    
    cy.intercept('GET', '**/api/tiktok', (req) => {
      requestCount++
      req.reply((res) => {
        res.delay(1000)
        res.send({
          statusCode: 200,
          body: {
            code: 200,
            data: {
              ad_account_default: 'test_account_123',
              ad_accounts: []
            }
          }
        })
      })
    }).as('slowTikTokUser')

    cy.mount(
      <Wrapper>
        <TestComponent />
      </Wrapper>
    )

    // Trigger multiple refetches quickly
    cy.get('[data-testid="refetch-btn"]').click()
    cy.get('[data-testid="refetch-btn"]').click()
    cy.get('[data-testid="refetch-btn"]').click()

    // Should only complete the last request
    cy.wait('@slowTikTokUser')
    cy.get('[data-testid="loading"]').should('contain', 'false')
  })

  it('should calculate isAccountSelected correctly', () => {
    cy.intercept('GET', '**/api/tiktok', {
      statusCode: 200,
      body: {
        code: 200,
        data: {
          ad_account_default: null,
          ad_accounts: []
        }
      }
    }).as('getTikTokUserNoAccount')

    cy.mount(
      <Wrapper>
        <TestComponent />
      </Wrapper>
    )

    cy.wait('@getTikTokUserNoAccount')
    
    cy.get('[data-testid="is-account-selected"]').should('contain', 'false')
  })
})
