import { Button } from '@/components/ui/button';
import { useSegmentContext } from '@/pages/context/SegmentContext';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { cn } from '@/lib/utils';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useTranslation } from 'react-i18next';
import LabelCustom from '@/components/Label';
import { NoData } from '@/components/NoData';
import { Input } from '@/components/ui/input';
import Modal from '@/components/Modal';
import { IAudienceDetail } from '@/types/audience';
import { useInfiniteQuery, useMutation } from '@tanstack/react-query';
import { QUERY_KEY } from '@/utils/constants';
import { get } from '@/apis/apiHelper';
import { ITiktokHistoryItem, ITiktokHistoryResponse } from '@/types/tiktok';
import { ENDPOINTS } from '@/apis/endpoints';
import { RiLoader2Line } from '@remixicon/react';
import { tiktokOauthApi } from '@/apis/tiktokOauth';
import { toast } from '@/hooks/use-toast';
import { LimitUpdateAudience } from '@/components/LimitUpdateAudience';

type Props = {
  detail: IAudienceDetail;
};

const UpdateCustomAudience: React.FC<Props> = ({ detail }: Props) => {
  const { audience_name, job_id } = detail;
  const { items } = useSegmentContext();
  const { t } = useTranslation();
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const loadMoreRef = useRef<HTMLDivElement>(null);
  const [openModal, setOpenModal] = useState<boolean>(false);
  const [isScrolling, setIsScrolling] = useState<boolean>(false);
  const [openWarning, setOpenWarning] = useState<boolean>(false);

  const [payload, setPayload] = useState<{
    job_id: number;
    segment_update_id: string;
  }>({ job_id: job_id, segment_update_id: '' });

  const newOptions = items.reduce<{ label: string; value: string; count: number }[]>(
    (acc, item) => {
      if (item.contact_quantity > 0) {
        acc.push({
          label: item.name,
          value: item.id,
          count: item.contact_quantity,
        });
      }
      return acc;
    },
    [],
  );

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading, isError } =
    useInfiniteQuery({
      queryKey: [QUERY_KEY.TIKTOK_LIST_SEGMENT, 'history', job_id],
      enabled: !!job_id && openModal,
      staleTime: 1000, // 1 second
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      queryFn: async ({ pageParam = 1 }) => {
        const response = await get<ITiktokHistoryResponse>({
          endpoint: ENDPOINTS.custom_audience.history(job_id),
          params: {
            page: pageParam,
            limit: 20,
          },
        });
        /* eslint-disable @typescript-eslint/no-explicit-any */
        return response.data?.data as any as ITiktokHistoryResponse;
      },
      getNextPageParam: (lastPage, allPages) => {
        if (!lastPage?.items || lastPage.items.length < 20) {
          return undefined; // No more pages
        }
        return allPages.length + 1;
      },
      initialPageParam: 1,
    });

  const allHistoryItems = data?.pages.flatMap((page) => page?.items || []) || [];

  // Intersection Observer for infinite scroll (more reliable than scroll events)
  useEffect(() => {
    const loadMoreElement = loadMoreRef.current;
    if (!loadMoreElement || !hasNextPage || isFetchingNextPage) {
      return;
    }

    const observer = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting && hasNextPage && !isFetchingNextPage && !isScrolling) {
          console.log('Load more element is visible, fetching next page...');
          setIsScrolling(true);
          fetchNextPage().finally(() => {
            setTimeout(() => setIsScrolling(false), 300);
          });
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
      },
    );

    observer.observe(loadMoreElement);

    return () => {
      observer.unobserve(loadMoreElement);
    };
  }, [fetchNextPage, hasNextPage, isFetchingNextPage, isScrolling]);

  // Handle scroll to load more data with spam prevention
  const handleScroll = useCallback(
    (e: React.UIEvent<HTMLDivElement>) => {
      const target = e.currentTarget;

      if (!target || isScrolling || isFetchingNextPage || !hasNextPage) {
        return;
      }

      const { scrollTop, scrollHeight, clientHeight } = target;
      const isNearBottom = scrollTop + clientHeight >= scrollHeight - 50; // Reduced threshold to 50px

      console.log('Scroll event:', {
        scrollTop,
        scrollHeight,
        clientHeight,
        isNearBottom,
        hasNextPage,
      });

      if (isNearBottom) {
        console.log('Fetching next page...');
        setIsScrolling(true);
        fetchNextPage().finally(() => {
          // Prevent spam by adding a small delay
          setTimeout(() => setIsScrolling(false), 300);
        });
      }
    },
    [fetchNextPage, hasNextPage, isFetchingNextPage, isScrolling],
  );

  const renderHistoryItem = (item: ITiktokHistoryItem) => {
    return (
      <p key={item.id} className="text-xs text-big360Color-neutral-700 mb-1">
        {item.segment_info?.name || 'Unknown Segment'}
      </p>
    );
  };

  const mutationUpdate = useMutation({
    mutationFn: tiktokOauthApi.updateAudience,
    onSuccess: () => {
      setOpenModal(false);
      toast({
        title: t('common.facebookAds.audiences.addCustomAudience'),
        status: 'success',
      });
    },
    onError: (e) => e,
  });

  const handleSubmit = () => {
    const { job_id, segment_update_id } = payload;

    const updatedAt = new Date(detail.updated_at);
    const currentTime = new Date();
    const timeDifferenceInHours = (currentTime.getTime() - updatedAt.getTime()) / (1000 * 60 * 60);

    if (timeDifferenceInHours < 1) {
      setOpenModal(false);
      setOpenWarning(true);
      return;
    }

    mutationUpdate.mutate({ job_id, segment_update_id });
  };

  return (
    <>
      <Modal
        openModal={openModal}
        onOpenChange={setOpenModal}
        onCloseModal={setOpenModal}
        trigger={
          <Button
            variant={'ghost'}
            className="w-fit px-2 py-0 m-0 h-auto text-big360Color-brand-500"
          >
            {t('common.update')}
          </Button>
        }
        title={
          <div className="flex items-center flex-col">
            <span className="font-medium text-big360Color-neutral-950">
              {t('audience.updateCustomAudience')}
            </span>
            <span className="text-sm font-normal text-big360Color-neutral-700">
              {t('audience.selectAnSegment')}
            </span>
          </div>
        }
        titleAlign={'center'}
        className="max-w-[650px] w-full h-[534px]"
      >
        <div className="flex flex-col gap-4">
          <div className="flex flex-col">
            <LabelCustom className="mb-2" label={t('audience.customAudienceName')} />
            <Input
              value={audience_name}
              disabled
              className={cn('outline-none border h-10 w-full p-3 rounded-xl text-sm')}
              placeholder={t('segment.selectSegment')}
            />
          </div>
          <div className="h-[170px] rounded-xl bg-big360Color-neutral-50 border-big360Color-neutral-100 mb-2 p-2">
            <p className="mb-1 text-xs text-big360Color-neutral-950 font-medium">
              {t('audience.included')}
            </p>
            <div
              ref={scrollContainerRef}
              className="overflow-auto h-[130px]"
              id={'history-tiktok'}
              onScroll={handleScroll}
            >
              {isLoading ? (
                <div className="flex justify-center items-center h-full">
                  <RiLoader2Line className="animate-spin" size={20} />
                </div>
              ) : isError ? (
                <div className="flex justify-center items-center h-full text-red-500 text-sm">
                  {t('common.error.loadData')}
                </div>
              ) : allHistoryItems.length === 0 ? (
                <NoData />
              ) : (
                <>
                  {allHistoryItems.map(renderHistoryItem)}

                  {/* Load more trigger element for Intersection Observer */}
                  {hasNextPage && <div ref={loadMoreRef} className="h-4 w-full" />}

                  {isFetchingNextPage && (
                    <div className="flex justify-center py-2">
                      <RiLoader2Line className="animate-spin" size={16} />
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
          <div className="flex flex-col mb-2">
            <LabelCustom
              isRequire={true}
              className="mb-2"
              label={t('common.facebookAds.audiences.segment')}
            />
            <Select
              onValueChange={(value) =>
                setPayload((prev) => ({ ...prev, segment_update_id: value }))
              }
            >
              <SelectTrigger className="w-full h-10 rounded-xl">
                <SelectValue placeholder={t('common.facebookAds.audiences.segmentPlaceholder')} />
              </SelectTrigger>
              <SelectContent className="max-h-[250px] overflow-auto p-2 rounded-xl">
                {!!newOptions.length ? (
                  newOptions.map((item) => (
                    <SelectItem
                      className={cn(
                        'text-sm p-2 rounded-md cursor-pointer',
                        item.value === payload.segment_update_id &&
                          '!bg-brand text-white hover:text-white focus:text-white',
                      )}
                      key={item.value}
                      value={item.value}
                    >
                      <p>
                        {item.label}
                        <span className="text-xs text-big360Color-neutral-500">
                          {' - '}
                          {Number(item.count).toLocaleString()} {t('common.contacts')}
                        </span>
                      </p>
                    </SelectItem>
                  ))
                ) : (
                  <NoData />
                )}
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-end justify-end gap-3">
            <Button
              onClick={() => setOpenModal(false)}
              className="px-3 py-1 rounded-xl w-full"
              variant={'secondary'}
              size={'lg'}
            >
              {t('common.button.cancel')}
            </Button>
            <Button
              onClick={handleSubmit}
              className="px-3 py-1 rounded-xl w-full"
              size={'lg'}
              disabled={mutationUpdate.isPending || !payload.segment_update_id}
            >
              {mutationUpdate.isPending ? (
                <RiLoader2Line className="animate-spin" />
              ) : (
                t('common.update')
              )}
            </Button>
          </div>
        </div>
      </Modal>
      <LimitUpdateAudience open={openWarning} setOpen={setOpenWarning} />
    </>
  );
};
export default UpdateCustomAudience;
