import React, { useEffect, useState } from 'react';
// import { useTranslation } from 'react-i18next';
import DataTable from '@/components/table/DataTable';
import { ColumnDef } from '@tanstack/react-table';
import { ITiktokHistoryResponse, ITiktokHistoryItem } from '@/types/tiktok';
import historyTiktokCol from '@/pages/TiktokAds/components/Column/historyTiktokCol';
import { TPagination } from '@/types/table';
import { Box } from '@/components/Box';
import { useQuery } from '@tanstack/react-query';
import { PAGE_SIZE, QUERY_KEY } from '@/utils/constants';
import { get } from '@/apis/apiHelper';
import { ENDPOINTS } from '@/apis/endpoints';
import { useTranslation } from 'react-i18next';

interface IHistoryTiktok {
  jobId: number;
  totalContact: number;
}

const HistoryTiktok: React.FC<IHistoryTiktok> = ({ jobId, totalContact }) => {
  const { t } = useTranslation();
  const [historyTiktokAds, setHistoryTiktokAds] = useState<ITiktokHistoryResponse>({
    count: 0,
    items: [],
  });
  const [pagination, setPagination] = useState<TPagination>({
    currentPage: 1,
    pageSize: PAGE_SIZE,
  });
  const { data: historyResponse, isLoading: loadingContactSegment } = useQuery({
    enabled: !!jobId,
    queryKey: [QUERY_KEY.TIKTOK_HISTORY, pagination],
    queryFn: () =>
      get<ITiktokHistoryItem>({
        endpoint: ENDPOINTS.custom_audience.history(jobId),
        params: {
          page: pagination.currentPage,
          limit: pagination.pageSize,
        },
      }),
  });

  useEffect(() => {
    if (historyResponse?.data?.data) {
      setHistoryTiktokAds({
        items: historyResponse.data.data.items || [],
        count: historyResponse.data.data.count || 0,
      });
    }
  }, [historyResponse?.data]);

  return (
    <div>
      <DataTable
        data={historyTiktokAds.items}
        columns={historyTiktokCol() as ColumnDef<ITiktokHistoryItem>[]}
        total={historyTiktokAds.count}
        loading={loadingContactSegment}
        pagination={pagination}
        setPagination={setPagination}
        className="h-[352px] max-w-[920px]"
      />
      <Box className="mb-1 mt-3">
        <p className="font-medium">{t('common.totalSegments')}:</p>
        <span className="font-medium">{Number(historyTiktokAds.count ?? 0).toLocaleString()}</span>
      </Box>
      <Box>
        <p className="font-medium">{t('common.totalContacts')}:</p>
        <span className="font-semibold text-lg">{Number(totalContact ?? 0).toLocaleString()}</span>
      </Box>
    </div>
  );
};

export default HistoryTiktok;
