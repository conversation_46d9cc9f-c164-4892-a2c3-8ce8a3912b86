// import AddAudiencePopup from './audiences/AddAudiencePopup';
import { useTiktokContext } from '../context/TiktokAuthContext';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import {
  RiArrowDownSLine,
  RiArrowLeftRightLine,
  RiLinkUnlinkM,
  RiNewsLine,
} from '@remixicon/react';
import { useState } from 'react';
// import SelectAccountPopup from './SelectAccountPopup';
import { cn } from '@/lib/utils';
import { useTranslation } from 'react-i18next';
import { TFilterAudienceTiktok } from '@/types/facebook';
import LogoutSocialPopup from '@/components/LogoutSocialPopup';
import SelectAccountPopup from '@/pages/TiktokAds/components/SelectAccountPopup';
import AddAudiencePopup from '@/components/CustomAudience/AddAudiencePopup';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from '@/hooks/use-toast';
import { tiktokOauthApi } from '@/apis/tiktokOauth';
import { QUERY_KEY } from '@/utils/constants';
import { DropdownWrapper } from '@/components/DropdownWrapper';
import { TitleTiktok } from '@/pages/TiktokAds/components/TitleTiktok';

interface IHeaderProps {
  filterPayload?: TFilterAudienceTiktok;
  setFilterPayload?: (value: TFilterAudienceTiktok) => void;
  ad_Account_id?: string;
}

const HeaderTiktok = ({ ...props }: IHeaderProps) => {
  const { setFilterPayload } = props;
  const { t } = useTranslation();
  const queryClient = useQueryClient();

  const { isLogin, isAccountSelected, adsAccount, logout } = useTiktokContext();
  const [showModal, setShowModal] = useState(false);
  const [showModalAddAudience, setShowModalAddAudience] = useState(false);

  const mutationAdd = useMutation({
    mutationFn: tiktokOauthApi.uploadAudience,
    onSuccess: () => {
      setShowModalAddAudience(false);
      queryClient.invalidateQueries({
        queryKey: [QUERY_KEY.TIKTOK_AUDIENCE],
      });
      if (!!setFilterPayload) {
        setFilterPayload({
          date_created_from: '',
          date_created_to: '',
          limit: 10,
          page: 1,
          search: '',
          platform: 'TT',
        });
      }

      toast({
        title: t('common.facebookAds.audiences.addCustomAudience'),
        status: 'success',
      });
    },
    onError: (e) => e,
  });

  const handleSubmit = (payload: { audience_name: string; segment_id: string }) => {
    const { audience_name, segment_id } = payload;
    mutationAdd.mutate({ audience_name, segment_id });
  };

  return (
    <div className="flex flex-col gap-0">
      <TitleTiktok />

      {isLogin && (
        <>
          <div
            className={cn(
              'flex items-center',
              isAccountSelected ? 'justify-between' : 'justify-end',
            )}
          >
            <DropdownWrapper
              triggerClassName={cn(
                'w-[218px] justify-between',
                !adsAccount?.ad_account_name && 'text-big360Color-neutral-500',
              )}
              trigger={
                <div className="flex justify-between items-center gap-2">
                  <div className="flex gap-1 h-5 items-center w-full border-r border-big360Color-neutral-300">
                    <div className="w-6 h-6 flex items-center justify-center">
                      <RiNewsLine
                        className="flex-shrink-0"
                        size={16}
                        color={!!adsAccount?.ad_account_name ? '#141416' : '#909498'}
                      />
                    </div>
                    <span className="truncate w-full text-start font-medium">
                      {!!adsAccount?.ad_account_id
                        ? adsAccount.ad_account_name
                        : t('common.facebookAds.switchAccount')}
                    </span>
                  </div>
                  <div className="bg-big360Color-neutral-100 flex h-7 w-7 border border-big360Color-neutral-100 items-center justify-center rounded-xl gap-2 p-2.5">
                    <RiArrowDownSLine
                      className="ml-auto flex-shrink-0"
                      size={20}
                      color={'#141416'}
                    />
                  </div>
                </div>
              }
              contentClassName={'w-[218px] p-2 flex flex-col gap-1'}
              alignOffset={0}
              sideOffset={10}
            >
              {isAccountSelected && (
                <DropdownMenuItem className="cursor-pointer h-7" onClick={() => setShowModal(true)}>
                  <RiArrowLeftRightLine size={20} color={'#20232C'} />
                  <span className="font-medium">{t('common.facebookAds.switchAccount')}</span>
                </DropdownMenuItem>
              )}
              <LogoutSocialPopup
                onLogout={logout}
                title={t('tiktokAds.disconnectTitle')}
                description={t('tiktokAds.disconnectDescription')}
                content={t('common.button.disconnect')}
                icon={<RiLinkUnlinkM color={'#141416'} />}
              />
            </DropdownWrapper>

            <div className="flex items-center gap-3">
              {isAccountSelected && (
                <AddAudiencePopup
                  loading={mutationAdd.isPending}
                  open={showModalAddAudience}
                  onSubmitAddSegment={handleSubmit}
                  setOpen={setShowModalAddAudience}
                />
              )}
            </div>
          </div>
        </>
      )}
      {showModal && <SelectAccountPopup open={showModal} setOpen={setShowModal} />}
    </div>
  );
};
export default HeaderTiktok;
