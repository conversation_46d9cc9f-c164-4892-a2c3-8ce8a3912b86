import EmptyTiktokDataView from './views/EmptyTiktokDataView';
import ContainerTiktokAds from './components/ContainerTiktokAds';
import Breadcrumb from '@/components/Breadcrumb';
import HeaderTik<PERSON> from '@/pages/TiktokAds/components/HeaderTiktok';
import { useTiktokContext } from '@/pages/TiktokAds/context/TiktokAuthContext';
import { useState } from 'react';
import Modal from '@/components/Modal';
import HistoryTiktok from '@/pages/TiktokAds/components/HistoryTiktok';
import UpdateCustomAudience from '@/pages/TiktokAds/components/UpdateCustomAudience';
import { IAudienceDetail } from '@/types/audience';
import { useTranslation } from 'react-i18next';

const TiktokAdsPage = () => {
  const { isAccountSelected } = useTiktokContext();
  const { t } = useTranslation();

  // Modal states
  const [historyModal, setHistoryModal] = useState<{
    isOpen: boolean;
    audienceDetail: IAudienceDetail | null;
  }>({
    isOpen: false,
    audienceDetail: null,
  });

  const [updateModal, setUpdateModal] = useState<{
    isOpen: boolean;
    audienceDetail: IAudienceDetail | null;
  }>({
    isOpen: false,
    audienceDetail: null,
  });

  const openHistoryModal = (detail: IAudienceDetail) => {
    setHistoryModal({
      isOpen: true,
      audienceDetail: detail,
    });
  };

  const closeHistoryModal = () => {
    setHistoryModal({
      isOpen: false,
      audienceDetail: null,
    });
  };

  const openUpdateModal = (detail: IAudienceDetail) => {
    setUpdateModal({
      isOpen: true,
      audienceDetail: detail,
    });
  };

  const closeUpdateModal = () => {
    setUpdateModal({
      isOpen: false,
      audienceDetail: null,
    });
  };

  return (
    <div className="w-full h-full flex flex-col">
      <Breadcrumb />

      {!isAccountSelected ? (
        <>
          <HeaderTiktok />
          <EmptyTiktokDataView />
        </>
      ) : (
        <ContainerTiktokAds
          onOpenHistoryModal={openHistoryModal}
          onOpenUpdateModal={openUpdateModal}
        />
      )}

      {/* History Modal */}
      <Modal
        openModal={historyModal.isOpen}
        onOpenChange={(open) => {
          if (!open) closeHistoryModal();
        }}
        className="max-w-[920px] w-full h-[592px]"
        title={
          <div className="h-[40px]">
            <p className="text-lg font-semibold text-big360Color-neutral-950">
              {t('common.updateHistory')}
            </p>
            <p className="text-sm font-normal text-big360Color-neutral-700">
              {historyModal.audienceDetail?.audience_name || t('common.customAudienceName')}
            </p>
          </div>
        }
      >
        {historyModal.audienceDetail && (
          <HistoryTiktok
            jobId={historyModal.audienceDetail.job_id}
            totalContact={historyModal.audienceDetail.total_records}
          />
        )}
      </Modal>

      {/* Update Modal */}
      <Modal
        openModal={updateModal.isOpen}
        onOpenChange={(open) => {
          if (!open) closeUpdateModal();
        }}
        titleAlign={'center'}
        className="max-w-[650px] w-full h-[534px]"
      >
        {updateModal.audienceDetail && (
          <UpdateCustomAudience
            detail={updateModal.audienceDetail}
            onClose={closeUpdateModal}
          />
        )}
      </Modal>
    </div>
  );
};

export default TiktokAdsPage;
